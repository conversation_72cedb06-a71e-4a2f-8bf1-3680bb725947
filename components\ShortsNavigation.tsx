import React from 'react';
import { ChevronUpIcon, ChevronDownIcon } from '@heroicons/react/24/outline';

interface ShortsNavigationProps {
  onPrevious: () => void;
  onNext: () => void;
  canGoPrevious: boolean;
  canGoNext: boolean;
  className?: string;
}

const ShortsNavigation: React.FC<ShortsNavigationProps> = ({
  onPrevious,
  onNext,
  canGoPrevious,
  canGoNext,
  className = ''
}) => {
  return (
    <div className={`flex flex-col space-y-2 ${className}`}>
      {/* Previous button */}
      <button
        onClick={onPrevious}
        disabled={!canGoPrevious}
        className={`p-3 rounded-full backdrop-blur-sm transition-all duration-200 ${
          canGoPrevious
            ? 'bg-white/20 text-white hover:bg-white/30 hover:scale-110'
            : 'bg-white/10 text-white/40 cursor-not-allowed'
        }`}
        aria-label="Previous short"
      >
        <ChevronUpIcon className="w-5 h-5" />
      </button>
      
      {/* Next button */}
      <button
        onClick={onNext}
        disabled={!canGoNext}
        className={`p-3 rounded-full backdrop-blur-sm transition-all duration-200 ${
          canGoNext
            ? 'bg-white/20 text-white hover:bg-white/30 hover:scale-110'
            : 'bg-white/10 text-white/40 cursor-not-allowed'
        }`}
        aria-label="Next short"
      >
        <ChevronDownIcon className="w-5 h-5" />
      </button>
    </div>
  );
};

export default ShortsNavigation;
